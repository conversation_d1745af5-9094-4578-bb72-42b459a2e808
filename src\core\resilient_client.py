"""
Interrupt-resilient file transfer client with resume capabilities.
"""

import socket
import json
import hashlib
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List, Tuple
from src.utils.logger import get_logger
from src.utils.transfer_state import TransferStateManager, TransferState
from src.utils.network_monitor import NetworkMonitor, ConnectionManager, ConnectionState


class ResilientFileTransferClient:
    """
    Enhanced file transfer client with interrupt resilience and resume capabilities.
    """
    
    def __init__(
        self,
        chunk_size: int = 8192,
        state_dir: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 2.0
    ):
        """
        Initialize the resilient file transfer client.
        
        Args:
            chunk_size: Size of data chunks for transfer
            state_dir: Directory to store transfer state files
            max_retries: Maximum retry attempts for failed operations
            retry_delay: Delay between retry attempts
        """
        self.chunk_size = chunk_size
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.logger = get_logger()
        
        # Initialize components
        self.state_manager = TransferStateManager(state_dir)
        self.network_monitor = NetworkMonitor()
        self.connection_manager = ConnectionManager(self.network_monitor)
        
        # Load existing transfer states
        self.state_manager.load_existing_transfers()
        
        # Transfer state
        self.current_transfer_id: Optional[str] = None
        self.paused_transfers: Dict[str, bool] = {}
        
        # Callbacks for GUI integration
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_transfer_progress: Optional[Callable] = None
        self.on_transfer_complete: Optional[Callable] = None
        self.on_transfer_error: Optional[Callable] = None
        self.on_transfer_paused: Optional[Callable] = None
        self.on_transfer_resumed: Optional[Callable] = None
        
        # Setup network monitor callbacks
        self.network_monitor.on_disconnected = self._on_network_disconnected
        self.network_monitor.on_reconnected = self._on_network_reconnected
    
    def connect(self, host: str, port: int) -> bool:
        """
        Connect to the file transfer server.
        
        Args:
            host: Server host address
            port: Server port number
            
        Returns:
            True if connected successfully
        """
        success = self.connection_manager.connect(host, port)
        
        if success and self.on_connected:
            self.on_connected(host, port)
        
        return success
    
    def disconnect(self):
        """Disconnect from the server."""
        self.connection_manager.disconnect()
        
        if self.on_disconnected:
            self.on_disconnected()
    
    def send_file(self, file_path: str, resume: bool = True) -> bool:
        """
        Send a file to the connected server with resume capability.
        
        Args:
            file_path: Path to the file to send
            resume: Whether to attempt resuming if transfer state exists
            
        Returns:
            True if file sent successfully
        """
        file_path = Path(file_path)
        
        if not file_path.exists() or not file_path.is_file():
            self.logger.error(f"Invalid file path: {file_path}")
            return False
        
        try:
            # Calculate file metadata
            file_size = file_path.stat().st_size
            file_checksum = self._calculate_file_checksum(file_path)
            
            # Check for existing transfer state
            transfer_id = None
            if resume:
                transfer_id = self._find_existing_transfer(file_path.name, file_size, file_checksum)
            
            if transfer_id:
                self.logger.info(f"Resuming transfer for {file_path.name}")
                return self._resume_file_transfer(transfer_id, file_path)
            else:
                self.logger.info(f"Starting new transfer for {file_path.name}")
                return self._start_new_transfer(file_path, file_size, file_checksum)
                
        except Exception as e:
            self.logger.error(f"Error sending file {file_path.name}: {e}")
            if self.on_transfer_error:
                self.on_transfer_error(file_path.name, str(e))
            return False
    
    def pause_transfer(self, transfer_id: Optional[str] = None) -> bool:
        """
        Pause an active transfer.
        
        Args:
            transfer_id: Transfer ID to pause (current transfer if None)
            
        Returns:
            True if paused successfully
        """
        if transfer_id is None:
            transfer_id = self.current_transfer_id
        
        if transfer_id:
            self.paused_transfers[transfer_id] = True
            self.logger.info(f"Paused transfer {transfer_id}")
            
            if self.on_transfer_paused:
                self.on_transfer_paused(transfer_id)
            
            return True
        
        return False
    
    def resume_transfer(self, transfer_id: str) -> bool:
        """
        Resume a paused transfer.
        
        Args:
            transfer_id: Transfer ID to resume
            
        Returns:
            True if resumed successfully
        """
        if transfer_id in self.paused_transfers:
            del self.paused_transfers[transfer_id]
            
            # Get transfer state
            if transfer_id in self.state_manager.active_transfers:
                state = self.state_manager.active_transfers[transfer_id]
                file_path = Path(state.download_path).parent / state.file_name
                
                self.logger.info(f"Resuming transfer {transfer_id}")
                
                if self.on_transfer_resumed:
                    self.on_transfer_resumed(transfer_id)
                
                return self._resume_file_transfer(transfer_id, file_path)
        
        return False
    
    def get_active_transfers(self) -> List[Dict[str, Any]]:
        """
        Get list of active transfers with their progress.
        
        Returns:
            List of transfer information dictionaries
        """
        transfers = []
        
        for transfer_id, state in self.state_manager.active_transfers.items():
            if not state.completed:
                progress = self.state_manager.get_progress(transfer_id)
                transfers.append({
                    'transfer_id': transfer_id,
                    'file_name': state.file_name,
                    'file_size': state.file_size,
                    'progress': progress,
                    'paused': transfer_id in self.paused_transfers,
                    'created': state.created_timestamp,
                    'last_updated': state.last_updated
                })
        
        return transfers
    
    def cleanup_completed_transfers(self) -> int:
        """
        Clean up completed transfer state files.
        
        Returns:
            Number of transfers cleaned up
        """
        cleaned_count = 0
        
        for transfer_id, state in list(self.state_manager.active_transfers.items()):
            if state.completed:
                if self.state_manager.cleanup_transfer(transfer_id):
                    cleaned_count += 1
        
        return cleaned_count
    
    def _start_new_transfer(self, file_path: Path, file_size: int, file_checksum: str) -> bool:
        """Start a new file transfer."""
        # Create transfer state
        transfer_id = self.state_manager.create_transfer_state(
            file_name=file_path.name,
            file_size=file_size,
            total_checksum=file_checksum,
            download_path=str(file_path.parent)
        )
        
        self.current_transfer_id = transfer_id
        
        # Send file metadata to server
        metadata = {
            "filename": file_path.name,
            "size": file_size,
            "checksum": file_checksum,
            "transfer_id": transfer_id,
            "resume": False
        }
        
        if not self._send_metadata(metadata):
            return False
        
        # Send file content in chunks
        return self._send_file_chunks(transfer_id, file_path, 0, file_size - 1)
    
    def _resume_file_transfer(self, transfer_id: str, file_path: Path) -> bool:
        """Resume an existing file transfer."""
        self.current_transfer_id = transfer_id
        state = self.state_manager.active_transfers[transfer_id]
        
        # Get missing ranges
        missing_ranges = self.state_manager.get_missing_ranges(transfer_id, self.chunk_size)
        
        if not missing_ranges:
            # Transfer is already complete
            self.state_manager.mark_completed(transfer_id)
            if self.on_transfer_complete:
                self.on_transfer_complete(file_path.name, True)
            return True
        
        # Send resume metadata to server
        metadata = {
            "filename": file_path.name,
            "size": state.file_size,
            "checksum": state.total_checksum,
            "transfer_id": transfer_id,
            "resume": True,
            "missing_ranges": missing_ranges
        }
        
        if not self._send_metadata(metadata):
            return False
        
        # Send missing chunks
        for start_byte, end_byte in missing_ranges:
            if transfer_id in self.paused_transfers:
                self.logger.info(f"Transfer {transfer_id} paused")
                return True
            
            if not self._send_file_chunks(transfer_id, file_path, start_byte, end_byte):
                return False
        
        # Mark as completed
        self.state_manager.mark_completed(transfer_id)
        
        if self.on_transfer_complete:
            self.on_transfer_complete(file_path.name, True)
        
        return True
    
    def _send_file_chunks(self, transfer_id: str, file_path: Path, start_byte: int, end_byte: int) -> bool:
        """Send file chunks for a specific byte range."""
        try:
            with open(file_path, 'rb') as f:
                f.seek(start_byte)
                current_pos = start_byte
                
                while current_pos <= end_byte:
                    # Check if transfer is paused
                    if transfer_id in self.paused_transfers:
                        return True
                    
                    # Calculate chunk size
                    remaining = end_byte - current_pos + 1
                    chunk_size = min(self.chunk_size, remaining)
                    
                    # Read chunk
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    
                    # Send chunk with retry
                    if not self.connection_manager.send_with_retry(chunk):
                        self.logger.error(f"Failed to send chunk at position {current_pos}")
                        return False
                    
                    # Update transfer state
                    chunk_end = current_pos + len(chunk) - 1
                    self.state_manager.add_segment(transfer_id, current_pos, chunk_end, chunk)
                    
                    current_pos += len(chunk)
                    
                    # Report progress
                    if self.on_transfer_progress:
                        state = self.state_manager.active_transfers[transfer_id]
                        progress = self.state_manager.get_progress(transfer_id)
                        total_sent = sum(seg.end_byte - seg.start_byte + 1 for seg in state.segments)
                        self.on_transfer_progress(file_path.name, progress, total_sent, state.file_size)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending file chunks: {e}")
            return False
    
    def _send_metadata(self, metadata: Dict[str, Any]) -> bool:
        """Send file metadata to server."""
        try:
            metadata_data = json.dumps(metadata).encode('utf-8')
            metadata_length = len(metadata_data)
            
            # Send metadata length then metadata
            length_bytes = metadata_length.to_bytes(4, byteorder='big')
            
            if not self.connection_manager.send_with_retry(length_bytes):
                return False
            
            if not self.connection_manager.send_with_retry(metadata_data):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending metadata: {e}")
            return False
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate MD5 checksum of entire file."""
        hash_md5 = hashlib.md5()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    def _find_existing_transfer(self, file_name: str, file_size: int, file_checksum: str) -> Optional[str]:
        """Find existing transfer state for a file."""
        for transfer_id, state in self.state_manager.active_transfers.items():
            if (state.file_name == file_name and 
                state.file_size == file_size and 
                state.total_checksum == file_checksum and 
                not state.completed):
                return transfer_id
        return None
    
    def _on_network_disconnected(self):
        """Handle network disconnection."""
        self.logger.warning("Network disconnected - pausing active transfers")
        
        if self.current_transfer_id:
            self.pause_transfer(self.current_transfer_id)
    
    def _on_network_reconnected(self):
        """Handle network reconnection."""
        self.logger.info("Network reconnected - transfers can be resumed")

        # Note: We don't automatically resume here to give user control

    def is_connected(self) -> bool:
        """Check if client is connected to server."""
        return self.connection_manager.connected

    def get_network_state(self) -> ConnectionState:
        """Get current network connection state."""
        return self.network_monitor.get_state()

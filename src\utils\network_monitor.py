"""
Network connectivity monitoring for interrupt-resilient file transfers.
"""

import socket
import threading
import time
from typing import Optional, Callable, List
from enum import Enum
from src.utils.logger import get_logger


class ConnectionState(Enum):
    """Network connection states."""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    RECONNECTING = "reconnecting"
    UNKNOWN = "unknown"


class NetworkMonitor:
    """Monitors network connectivity and handles reconnection."""
    
    def __init__(
        self,
        check_interval: float = 5.0,
        timeout: float = 3.0,
        max_retries: int = 3
    ):
        """
        Initialize the network monitor.
        
        Args:
            check_interval: Seconds between connectivity checks
            timeout: Timeout for connectivity tests
            max_retries: Maximum reconnection attempts
        """
        self.check_interval = check_interval
        self.timeout = timeout
        self.max_retries = max_retries
        self.logger = get_logger()
        
        # State
        self.state = ConnectionState.UNKNOWN
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # Target connection info
        self.target_host: Optional[str] = None
        self.target_port: Optional[int] = None
        
        # Callbacks
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_reconnecting: Optional[Callable] = None
        self.on_reconnected: Optional[Callable] = None
        
        # Test hosts for general internet connectivity
        self.test_hosts = [
            ("*******", 53),      # Google DNS
            ("*******", 53),      # Cloudflare DNS
            ("**************", 53) # OpenDNS
        ]
    
    def start_monitoring(self, target_host: str, target_port: int):
        """
        Start monitoring network connectivity to a specific target.
        
        Args:
            target_host: Host to monitor connection to
            target_port: Port to monitor connection to
        """
        self.target_host = target_host
        self.target_port = target_port
        
        if self.monitoring:
            self.stop_monitoring()
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"Started network monitoring for {target_host}:{target_port}")
    
    def stop_monitoring(self):
        """Stop network monitoring."""
        self.monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
        
        self.logger.info("Stopped network monitoring")
    
    def test_connectivity(self, host: Optional[str] = None, port: Optional[int] = None) -> bool:
        """
        Test connectivity to a specific host or general internet.
        
        Args:
            host: Host to test (uses target_host if None)
            port: Port to test (uses target_port if None)
            
        Returns:
            True if connected, False otherwise
        """
        # Test specific target if provided
        if host and port:
            return self._test_host_port(host, port)
        
        # Test target connection if available
        if self.target_host and self.target_port:
            if self._test_host_port(self.target_host, self.target_port):
                return True
        
        # Test general internet connectivity
        return self._test_internet_connectivity()
    
    def wait_for_connection(self, timeout: float = 30.0) -> bool:
        """
        Wait for network connection to be restored.
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            True if connection restored, False if timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.test_connectivity():
                return True
            time.sleep(1.0)
        
        return False
    
    def attempt_reconnection(self) -> bool:
        """
        Attempt to reconnect to the target.
        
        Returns:
            True if reconnection successful
        """
        if not self.target_host or not self.target_port:
            return False
        
        self._set_state(ConnectionState.RECONNECTING)
        
        for attempt in range(1, self.max_retries + 1):
            self.logger.info(f"Reconnection attempt {attempt}/{self.max_retries}")
            
            if self._test_host_port(self.target_host, self.target_port):
                self._set_state(ConnectionState.CONNECTED)
                if self.on_reconnected:
                    self.on_reconnected()
                return True
            
            if attempt < self.max_retries:
                time.sleep(min(2 ** attempt, 10))  # Exponential backoff, max 10s
        
        self._set_state(ConnectionState.DISCONNECTED)
        return False
    
    def get_state(self) -> ConnectionState:
        """Get current connection state."""
        return self.state
    
    def is_connected(self) -> bool:
        """Check if currently connected."""
        return self.state == ConnectionState.CONNECTED
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        last_state = ConnectionState.UNKNOWN
        
        while self.monitoring:
            try:
                # Test connectivity
                is_connected = self.test_connectivity()
                
                if is_connected:
                    new_state = ConnectionState.CONNECTED
                else:
                    new_state = ConnectionState.DISCONNECTED
                
                # Check for state changes
                if new_state != last_state:
                    self._set_state(new_state)
                    
                    if new_state == ConnectionState.CONNECTED and last_state == ConnectionState.DISCONNECTED:
                        if self.on_connected:
                            self.on_connected()
                    elif new_state == ConnectionState.DISCONNECTED and last_state == ConnectionState.CONNECTED:
                        if self.on_disconnected:
                            self.on_disconnected()
                
                last_state = new_state
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in network monitoring loop: {e}")
                time.sleep(self.check_interval)
    
    def _test_host_port(self, host: str, port: int) -> bool:
        """Test connectivity to a specific host and port."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(self.timeout)
                result = sock.connect_ex((host, port))
                return result == 0
        except Exception:
            return False
    
    def _test_internet_connectivity(self) -> bool:
        """Test general internet connectivity using multiple test hosts."""
        for host, port in self.test_hosts:
            if self._test_host_port(host, port):
                return True
        return False
    
    def _set_state(self, new_state: ConnectionState):
        """Set connection state and log changes."""
        if new_state != self.state:
            old_state = self.state
            self.state = new_state
            self.logger.info(f"Network state changed: {old_state.value} -> {new_state.value}")


class ConnectionManager:
    """Manages socket connections with automatic reconnection."""
    
    def __init__(self, monitor: NetworkMonitor):
        """
        Initialize connection manager.
        
        Args:
            monitor: Network monitor instance
        """
        self.monitor = monitor
        self.logger = get_logger()
        
        # Connection state
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.host: Optional[str] = None
        self.port: Optional[int] = None
        
        # Setup monitor callbacks
        self.monitor.on_disconnected = self._on_network_disconnected
        self.monitor.on_reconnected = self._on_network_reconnected
    
    def connect(self, host: str, port: int, timeout: float = 10.0) -> bool:
        """
        Connect to host with automatic monitoring.
        
        Args:
            host: Target host
            port: Target port
            timeout: Connection timeout
            
        Returns:
            True if connected successfully
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(timeout)
            self.socket.connect((host, port))
            
            self.connected = True
            self.host = host
            self.port = port
            
            # Start monitoring this connection
            self.monitor.start_monitoring(host, port)
            
            self.logger.info(f"Connected to {host}:{port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to {host}:{port}: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect and stop monitoring."""
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        self.monitor.stop_monitoring()
        self.logger.info("Disconnected")
    
    def send_with_retry(self, data: bytes, max_retries: int = 3) -> bool:
        """
        Send data with automatic retry on network errors.
        
        Args:
            data: Data to send
            max_retries: Maximum retry attempts
            
        Returns:
            True if sent successfully
        """
        for attempt in range(max_retries + 1):
            try:
                if not self.connected or not self.socket:
                    if not self._attempt_reconnect():
                        continue
                
                self.socket.send(data)
                return True
                
            except (socket.error, ConnectionError) as e:
                self.logger.warning(f"Send failed (attempt {attempt + 1}): {e}")
                self.connected = False
                
                if attempt < max_retries:
                    if not self._attempt_reconnect():
                        time.sleep(1)
        
        return False
    
    def recv_with_retry(self, size: int, max_retries: int = 3) -> Optional[bytes]:
        """
        Receive data with automatic retry on network errors.
        
        Args:
            size: Number of bytes to receive
            max_retries: Maximum retry attempts
            
        Returns:
            Received data or None if failed
        """
        for attempt in range(max_retries + 1):
            try:
                if not self.connected or not self.socket:
                    if not self._attempt_reconnect():
                        continue
                
                data = self.socket.recv(size)
                return data
                
            except (socket.error, ConnectionError) as e:
                self.logger.warning(f"Receive failed (attempt {attempt + 1}): {e}")
                self.connected = False
                
                if attempt < max_retries:
                    if not self._attempt_reconnect():
                        time.sleep(1)
        
        return None
    
    def _attempt_reconnect(self) -> bool:
        """Attempt to reconnect to the last known host/port."""
        if not self.host or not self.port:
            return False
        
        self.logger.info("Attempting to reconnect...")
        return self.connect(self.host, self.port)
    
    def _on_network_disconnected(self):
        """Handle network disconnection."""
        self.logger.warning("Network disconnection detected")
        self.connected = False
    
    def _on_network_reconnected(self):
        """Handle network reconnection."""
        self.logger.info("Network reconnection detected")
        # Connection will be re-established on next send/recv attempt
